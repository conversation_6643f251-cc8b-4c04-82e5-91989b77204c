import ClientEntity, { ClientEntityParams } from '#/lib/biizi/domain/aggregates/Client/Client.Entity';
import ApplicationRegistry from '#composition/Application.Registry';

async function apply(params: Omit<ClientEntityParams, 'createdAt' | 'updatedAt' | 'id'>): Promise<ClientEntity> {
  const newClient = await ApplicationRegistry.ClientRepository.createClient(params);

  return newClient;
}

const CreateClientUseCase = {
  apply,
};

export default CreateClientUseCase;
