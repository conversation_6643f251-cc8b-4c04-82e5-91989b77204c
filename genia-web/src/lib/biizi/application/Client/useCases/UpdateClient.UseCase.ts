import ClientEntity, { ClientEntityParams } from '#/lib/biizi/domain/aggregates/Client/Client.Entity';
import ApplicationRegistry from '#composition/Application.Registry';

async function apply({ id, params }: { id: string; params: Partial<ClientEntityParams> }): Promise<ClientEntity> {
  const updatedClient = await ApplicationRegistry.ClientRepository.updateClient({ id, params });
  return updatedClient;
}

const UpdateClientUseCase = { apply };

export default UpdateClientUseCase;
