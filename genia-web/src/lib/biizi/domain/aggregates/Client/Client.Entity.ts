import ClientContactInformation from '#/lib/biizi/domain/aggregates/Client/ClientContactInformation.ValueObject';

export interface ClientEntityParams {
  id: string;
  name: string;
  tributaryId: string | null;
  clientCompanyId: string | null;
  storeDiscounts: string[];
  contactInformation: ClientContactInformation | null;
  createdAt: Date;
  updatedAt: Date;
}

export default class ClientEntity {
  id: string;

  name: string;

  tributaryId: string | null;

  clientCompanyId: string | null;

  storeDiscounts: string[];

  contactInformation: ClientContactInformation | null;

  createdAt: Date;

  updatedAt: Date;

  constructor(params: ClientEntityParams) {
    this.id = params.id;
    this.name = params.name;
    this.tributaryId = params.tributaryId;
    this.clientCompanyId = params.clientCompanyId;
    this.storeDiscounts = params.storeDiscounts;
    this.contactInformation = params.contactInformation;
    this.createdAt = params.createdAt;
    this.updatedAt = params.updatedAt;
  }

  setName(name: string): void {
    if (!name || name.trim() === '') {
      throw new Error('El nombre del cliente no puede estar vacío');
    }
    this.name = name.trim();
    this.updatedAt = new Date();
  }

  setTributaryId(tributaryId: string | null): void {
    if (tributaryId !== null && (!tributaryId || tributaryId.trim() === '')) {
      throw new Error('El ID tributario del cliente no puede estar vacío');
    }
    this.tributaryId = tributaryId ? tributaryId.trim() : null;
    this.updatedAt = new Date();
  }

  setContactInformation(contactInformation: ClientContactInformation | null): ClientEntity {
    return new ClientEntity({
      ...this,
      contactInformation,
    });
  }
}

// import { ClientContactInformation } from './ClientContactInformation.ValueObject';

// export enum ClientType {
//   CLIENT = 'client',
//   COMPANY = 'company'
// }

// Client type for application layer compatibility
export type Client = {
  id: string;
  name: string;
  tributaryId: string | null;
  clientCompanyId: string | null;
  storeDiscounts: string[];
  contactInformation: ClientContactInformation | null;
  createdAt: Date;
  updatedAt: Date;
};

// export default class ClientEntity {
//   readonly id: string;

//   private name: string;

//   private tributaryId: string;

//   private clientCompanyId: string | null;

//   private storeDiscountIds: string[]; // TODO: Migrate to reference store discounts by ID only

//   private contactInformation: ClientContactInformation | null;

//   readonly createdAt: Date;

//   constructor(
//     id: string,
//     name: string,
//     tributaryId: string,
//     clientCompanyId: string | null,
//     storeDiscountIds: string[],
//     contactInformation: ClientContactInformation | null,
//     createdAt: Date,
//   ) {
//     // Domain validations
//     if (!id || id.trim() === '') {
//       throw new Error('El ID del cliente es requerido');
//     }
//     if (!name || name.trim() === '') {
//       throw new Error('El nombre del cliente es requerido');
//     }
//     if (!tributaryId || tributaryId.trim() === '') {
//       throw new Error('El ID tributario del cliente es requerido');
//     }

//     this.id = id;
//     this.name = name.trim();
//     this.tributaryId = tributaryId.trim();
//     this.clientCompanyId = clientCompanyId;
//     this.storeDiscountIds = [...storeDiscountIds];
//     this.contactInformation = contactInformation;
//     this.createdAt = createdAt;
//   }

//   // Getters for accessing private properties
//   getName(): string {
//     return this.name;
//   }

//   getTributaryId(): string {
//     return this.tributaryId;
//   }

//   getClientCompanyId(): string | null {
//     return this.clientCompanyId;
//   }

//   getStoreDiscountIds(): string[] {
//     return [...this.storeDiscountIds];
//   }

//   getContactInformation(): ClientContactInformation | null {
//     return this.contactInformation;
//   }

//   // Domain operations
//   public setContactInformation(contactInformation: ClientContactInformation): void {
//     this.contactInformation = contactInformation;
//   }

//   public updateName(name: string): void {
//     if (!name || name.trim() === '') {
//       throw new Error('El nombre del cliente no puede estar vacío');
//     }
//     this.name = name.trim();
//   }

//   public updateTributaryId(tributaryId: string): void {
//     if (!tributaryId || tributaryId.trim() === '') {
//       throw new Error('El ID tributario del cliente no puede estar vacío');
//     }
//     this.tributaryId = tributaryId.trim();
//   }

//   public assignStoreDiscount(storeDiscountId: string): void {
//     if (!storeDiscountId || storeDiscountId.trim() === '') {
//       throw new Error('El ID del descuento de tienda es requerido');
//     }
//     if (!this.storeDiscountIds.includes(storeDiscountId)) {
//       this.storeDiscountIds.push(storeDiscountId);
//     }
//   }

//   public removeStoreDiscount(storeDiscountId: string): void {
//     this.storeDiscountIds = this.storeDiscountIds.filter((id) => id !== storeDiscountId);
//   }

//   public hasStoreDiscount(storeDiscountId: string): boolean {
//     return this.storeDiscountIds.includes(storeDiscountId);
//   }

//   // Legacy compatibility - will be deprecated
//   get storeDiscounts(): string[] {
//     return this.getStoreDiscountIds();
//   }

//   get updatedAt(): Date {
//     // For backward compatibility, return createdAt as updatedAt is not useful in FE
//     return this.createdAt;
//   }
// }
