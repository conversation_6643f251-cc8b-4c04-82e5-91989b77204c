import { Column, Row } from '@pitsdepot/storybook';
import { motion } from 'framer-motion';
import { useMemo, useState } from 'react';

import ClientEntity from '#/lib/biizi/domain/aggregates/Client/Client.Entity';
import ClientContactInformation from '#/lib/biizi/domain/aggregates/Client/ClientContactInformation.ValueObject';
import ClientsFilters from '#/lib/biizi/ui/components/ClientsFilters.Component';
import { useFindClients } from '#/lib/biizi/ui/hooks/Client.Hook';
import { usePagination } from '#/lib/biizi/ui/hooks/usePagination.Hook';
import { AnchorCell } from '#appComponent/table/TableCells.Component';
import ApplicationRegistry from '#composition/Application.Registry';

import ClientListContent from '../components/ClientListContent.Component';
import ClientsHeader from '../components/ClientsHeader.Component';
import ViewToggle from '../components/ViewToggle.Component';

// Interface for table display with extracted contact info
interface ClientTableData {
  id: string;
  name: string;
  tributaryId: string | null;
  clientCompanyId: string | null;
  contactInformation: ClientContactInformation | null;
  extractedEmail?: string;
  extractedPhone?: string;
  extractedCity?: string;
  lastContact?: string;
}
// import ClientsResume from '#/lib/biizi/ui/layout/ClientsResume';

const itemsPerPage = 12;
const columns: Column[] = [
  {
    header: 'Nombre',
    dataKey: 'name',
    width: '40%',
    renderer: ({ value, url }) => <AnchorCell url={url} value={value} />,
  },
  {
    header: 'Email',
    dataKey: 'email',
    width: '20%',
  },
  {
    header: 'Teléfono',
    dataKey: 'phone',
    width: '15%',
  },
  {
    header: 'Ciudad',
    dataKey: 'city',
    width: '15%',
  },
  {
    header: 'Último contacto',
    dataKey: 'lastContact',
    width: '10%',
  },
];

function mapper(itemsForTable: ClientTableData[]): Row[] {
  return itemsForTable.map((c) => {
    // Use the pre-extracted contact info from ClientTableData
    const email = c.extractedEmail || '';
    const phone = c.extractedPhone || '';
    const city = c.extractedCity || '';

    return {
      id: c.id,
      name: { value: c.name, url: ApplicationRegistry.PathService.clients.viewClient(c.id) },
      email,
      phone,
      city,
      lastContact: c.lastContact || '',
    };
  });
}

export default function ClientListLayout() {
  const [search, setSearch] = useState('');
  const [viewMode, setViewMode] = useState<'cards' | 'table'>('cards');
  const {
    currentPage,
    offset,
    handlePageChange,
  } = usePagination(itemsPerPage);

  const paginatedLimit = itemsPerPage;
  const paginatedOffset = offset;

  // Use the read wrapper (like integrations) so repository calls are done via a use-case hook
  const { data: clientsData, isLoading: clientsLoading } = useFindClients();

  const allClients: ClientTableData[] = useMemo(() => {
    if (!clientsData || clientsData.length === 0) return [];

    return (clientsData as ClientEntity[]).map((it): ClientTableData => {
      const contact = it.contactInformation;

      // Extract contact information in a type-safe way
      const extractedEmail = contact?.mainEmail || contact?.billingEmail || contact?.purchasesEmail || contact?.salesEmail || undefined;
      const extractedPhone = contact?.mainPhoneNumber || contact?.billingPhoneNumber || contact?.purchasesPhoneNumber || contact?.salesPhoneNumber || undefined;
      const extractedCity = contact?.mainAddress || contact?.shippingAddress || contact?.billingAddress || undefined;

      return {
        id: it.id,
        name: it.name,
        tributaryId: it.tributaryId,
        clientCompanyId: it.clientCompanyId,
        contactInformation: contact,
        // Add the extracted fields for easy access
        extractedEmail,
        extractedPhone,
        extractedCity,
        lastContact: undefined, // This would need to come from another source
      };
    });
  }, [clientsData]);

  const pagItems = allClients.slice(paginatedOffset, paginatedOffset + paginatedLimit) ?? [];
  const loading = clientsLoading;
  const paginatedTotalNumberOfItems = allClients.length;

  const serverClients: ClientTableData[] = useMemo(() => pagItems, [pagItems]);

  const filtered = useMemo(() => {
    const list = serverClients.length ? serverClients : [];
    return list.filter((c) => {
      const email = c.extractedEmail || '';
      const company = c.clientCompanyId || '';
      if (search && !`${c.name} ${email} ${company}`.toLowerCase().includes(search.toLowerCase())) return false;
      return true;
    });
  }, [serverClients, search]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.35 }}
      className="p-6"
    >
      <ClientsHeader />

      {/* <ClientsResume serverClients={serverClients} totalNumberOfItems={paginatedTotalNumberOfItems} /> */}

      <ClientsFilters
        search={search}
        setSearch={setSearch}
        rightActions={(
          <ViewToggle viewMode={viewMode} setViewMode={setViewMode} />
         )}
       />

      <ClientListContent
        loading={loading}
        viewMode={viewMode}
        filtered={filtered}
        paginatedTotalNumberOfItems={paginatedTotalNumberOfItems}
        columns={columns}
        mapper={mapper}
        tableCurrentPage={currentPage}
        handleTablePageChange={handlePageChange}
        cardsCurrentPage={currentPage}
        handleCardsPageChange={handlePageChange}
        itemsPerPageCards={itemsPerPage}
      />
    </motion.div>
  );
}
