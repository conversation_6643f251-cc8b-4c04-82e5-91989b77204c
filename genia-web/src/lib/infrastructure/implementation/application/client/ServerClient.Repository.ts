import { ClientRepositoryCreateCommands } from '#/lib/biizi/application/Client/repositories/Client.Repository';
import ClientEntity, { ClientEntityParams } from '#/lib/biizi/domain/aggregates/Client/Client.Entity';
import ClientContactInformation from '#/lib/biizi/domain/aggregates/Client/ClientContactInformation.ValueObject';
import { Notification } from '#appComponent/common/Notification.Component';
import { MSG_ERROR_TYPES } from '#application/types/Notification.Type';
import TextService from '#composition/textService/Text.Service';
import type { ClientsPayload } from '#infrastructure/api/http/Clients.Https';
import { ClientsHttps } from '#infrastructure/api/http/Clients.Https';
import ClientsQuery from '#infrastructure/api/readModel/queries/Clients.Query';

interface ClientResponse {
  id: string;
  name: string;
  tributaryId?: string | null;
  clientCompanyId?: string | null;
  storeDiscounts?: string[];
  // read-model can return camelCase 'contactInformation' or snake_case 'contact_information'
  contactInformation?: Partial<ClientContactInformation> | null;
  contact_information?: Partial<ClientContactInformation> | null;
  createdAt: string;
  updatedAt: string;
}

async function createClient(command: ClientRepositoryCreateCommands): Promise<ClientEntity> {
  const clientData = command;
  const text = TextService.getText();

  try {
    // Narrow the incoming data to a richer type that may include optional http-payload fields
    type ClientCreateInput =
      Omit<ClientEntityParams, 'createdAt' | 'updatedAt' | 'id'> &
      Partial<
        Pick<ClientsPayload, 'clientCompanyId' | 'storeDiscounts' | 'notes' | 'contactInformation'>
      > &
      {
        client_company?: { name?: string } | null;
      };
    const input = clientData as unknown as ClientCreateInput;

    // Build a payload that conforms to (a partial) ClientsPayload without using `any`
    const payload: Partial<ClientsPayload> & { name: string } = {
      name: (input.name ?? '').trim(),
      tributaryId: input.tributaryId === '' ? null : (input.tributaryId ?? null),
      clientCompanyId: input.clientCompanyId ?? input.client_company?.name ?? null,
      storeDiscounts: input.storeDiscounts ?? [],
      notes: input.notes ?? null,
      ...(input.contactInformation ? { contactInformation: input.contactInformation } : {}),
    };

    const response = await ClientsHttps.postClients([payload]);

    // The API returns an array, so we take the first element
    const clientResponse: ClientResponse = response.data[0];

    // Show success notification
    Notification({
      message: text.clients.addSuccess,
      type: MSG_ERROR_TYPES.SUCCESS,
    });

    // Transform the HTTP response into a ClientEntity
    const srcContact = clientResponse.contactInformation ?? clientResponse.contact_information ?? {};

    // Create ClientContactInformation value object if we have contact data
    let contactInformation: ClientContactInformation | null = null;
    if (srcContact.mainEmail) {
      contactInformation = new ClientContactInformation({
        mainEmail: srcContact.mainEmail,
        mainPhoneNumber: srcContact.mainPhoneNumber || undefined,
        mainAddress: srcContact.mainAddress || undefined,
        representativeName: undefined, // Not available in HTTP response
        billingEmail: srcContact.billingEmail || undefined,
        billingPhoneNumber: srcContact.billingPhoneNumber || undefined,
        billingWhatsapp: srcContact.billingWhatsapp || undefined,
        billingAddress: srcContact.billingAddress || undefined,
        purchasesEmail: srcContact.purchasesEmail || undefined,
        purchasesPhoneNumber: srcContact.purchasesPhoneNumber || undefined,
        purchasesWhatsapp: srcContact.purchasesWhatsapp || undefined,
        salesEmail: srcContact.salesEmail || undefined,
        salesPhoneNumber: srcContact.salesPhoneNumber || undefined,
        salesWhatsapp: srcContact.salesWhatsapp || undefined,
        shippingAddress: srcContact.shippingAddress || undefined,
      });
    }

    return new ClientEntity({
      id: clientResponse.id,
      name: clientResponse.name,
      tributaryId: clientResponse.tributaryId ?? null,
      clientCompanyId: clientResponse.clientCompanyId ?? null,
      storeDiscounts: clientResponse.storeDiscounts || [],
      contactInformation,
      createdAt: new Date(clientResponse.createdAt),
      updatedAt: new Date(clientResponse.updatedAt),
    });
  } catch (error) {
    // Show error notification
    Notification({
      message: text.clients.addFailed,
      type: MSG_ERROR_TYPES.ERROR,
    });

    // Re-throw the error so it can be handled by the calling code
    throw error;
  }
}

async function updateClient(params: { id: string; params: Partial<ClientEntityParams> }): Promise<ClientEntity> {
  const { id, params: clientData } = params;
  const text = TextService.getText();

  try {
    // Map Partial<Client> into Partial<ClientsPayload> safely
    type ClientUpdateInput =
      Partial<ClientEntityParams> &
      Partial<
        Pick<ClientsPayload, 'clientCompanyId' | 'storeDiscounts' | 'notes' | 'contactInformation'>
      > &
      {
        client_company?: { name?: string } | null;
      };
    const input = clientData as unknown as ClientUpdateInput;

    const updatePayload: Partial<ClientsPayload> = {};

    if (typeof input.name === 'string') updatePayload.name = input.name;
    if ('tributaryId' in input) updatePayload.tributaryId = input.tributaryId ?? null;
    // Prefer explicit clientCompanyId if provided, otherwise map from client_company.name
    if ('clientCompanyId' in input && input.clientCompanyId !== undefined) updatePayload.clientCompanyId = input.clientCompanyId as string | null;
    else if (input.client_company) updatePayload.clientCompanyId = input.client_company.name ?? null;
    if (input.storeDiscounts !== undefined) updatePayload.storeDiscounts = input.storeDiscounts;
    if (input.notes !== undefined) updatePayload.notes = input.notes ?? null;
    if (input.contactInformation !== undefined) updatePayload.contactInformation = input.contactInformation as ClientsPayload['contactInformation'];

    const response = await ClientsHttps.updateClient(id, updatePayload);

    // API should return the updated client object
    const clientResponse: ClientResponse = response.data;

    // Show success notification
    Notification({
      message: text.clients.updateSuccess,
      type: MSG_ERROR_TYPES.SUCCESS,
    });

    // Normalize contact information (camelCase or snake_case)
    const srcContact = clientResponse.contactInformation ?? clientResponse.contact_information ?? {};

    // Create ClientContactInformation value object if we have contact data
    let contactInformation: ClientContactInformation | null = null;
    if (srcContact.mainEmail) {
      contactInformation = new ClientContactInformation({
        mainEmail: srcContact.mainEmail,
        mainPhoneNumber: srcContact.mainPhoneNumber || undefined,
        mainAddress: srcContact.mainAddress || undefined,
        representativeName: undefined, // Not available in HTTP response
        billingEmail: srcContact.billingEmail || undefined,
        billingPhoneNumber: srcContact.billingPhoneNumber || undefined,
        billingWhatsapp: srcContact.billingWhatsapp || undefined,
        billingAddress: srcContact.billingAddress || undefined,
        purchasesEmail: srcContact.purchasesEmail || undefined,
        purchasesPhoneNumber: srcContact.purchasesPhoneNumber || undefined,
        purchasesWhatsapp: srcContact.purchasesWhatsapp || undefined,
        salesEmail: srcContact.salesEmail || undefined,
        salesPhoneNumber: srcContact.salesPhoneNumber || undefined,
        salesWhatsapp: srcContact.salesWhatsapp || undefined,
        shippingAddress: srcContact.shippingAddress || undefined,
      });
    }

    return new ClientEntity({
      id: clientResponse.id,
      name: clientResponse.name,
      tributaryId: clientResponse.tributaryId ?? null,
      clientCompanyId: clientResponse.clientCompanyId ?? null,
      storeDiscounts: clientResponse.storeDiscounts ?? [],
      contactInformation,
      createdAt: new Date(clientResponse.createdAt),
      updatedAt: new Date(clientResponse.updatedAt),
    });
  } catch (error) {
    Notification({
      message: text.clients.updateFailed,
      type: MSG_ERROR_TYPES.ERROR,
    });

    throw error;
  }
}

async function findAllClients(): Promise<ClientEntity[]> {
  const clients = await ClientsQuery.getClients();

  return clients.map((clientResponse: ClientResponse) => {
    const srcContact = clientResponse.contactInformation ?? clientResponse.contact_information ?? {};

    // Create ClientContactInformation value object if we have contact data
    let contactInformation: ClientContactInformation | null = null;
    if (srcContact.mainEmail) {
      contactInformation = new ClientContactInformation({
        mainEmail: srcContact.mainEmail,
        mainPhoneNumber: srcContact.mainPhoneNumber || undefined,
        mainAddress: srcContact.mainAddress || undefined,
        representativeName: undefined, // Not available in HTTP response
        billingEmail: srcContact.billingEmail || undefined,
        billingPhoneNumber: srcContact.billingPhoneNumber || undefined,
        billingWhatsapp: srcContact.billingWhatsapp || undefined,
        billingAddress: srcContact.billingAddress || undefined,
        purchasesEmail: srcContact.purchasesEmail || undefined,
        purchasesPhoneNumber: srcContact.purchasesPhoneNumber || undefined,
        purchasesWhatsapp: srcContact.purchasesWhatsapp || undefined,
        salesEmail: srcContact.salesEmail || undefined,
        salesPhoneNumber: srcContact.salesPhoneNumber || undefined,
        salesWhatsapp: srcContact.salesWhatsapp || undefined,
        shippingAddress: srcContact.shippingAddress || undefined,
      });
    }

    return new ClientEntity({
      id: clientResponse.id,
      name: clientResponse.name,
      tributaryId: clientResponse.tributaryId ?? null,
      clientCompanyId: clientResponse.clientCompanyId ?? null,
      storeDiscounts: clientResponse.storeDiscounts ?? [],
      contactInformation,
      createdAt: new Date(clientResponse.createdAt),
      updatedAt: new Date(clientResponse.updatedAt),
    });
  });
}

const ServerClientRepository = {
  createClient,
  updateClient,
  findAllClients,
};

export default ServerClientRepository;
